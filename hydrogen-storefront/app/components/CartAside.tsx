import {Await} from 'react-router-dom';
import {Suspense} from 'react';
import type {CartApiQueryFragment} from 'storefrontapi.generated';
import {Aside, useAside} from '~/components/Aside';
import {Button} from '~/components/Button';
import {CartMain} from '~/components/CartMain';

type CartAsideProps = {
  cart: Promise<CartApiQueryFragment | null>;
};

function CartAside({cart}: CartAsideProps) {
  return (
    <Aside heading="CART" type="cart">
      <Suspense fallback={<p>Loading cart ...</p>}>
        <Await resolve={cart}>
          {(cart: CartApiQueryFragment | null) => {
            if (!cart || !cart.totalQuantity) {
              return <CartEmpty />;
            }
            return <CartMain layout="aside" cart={cart} />;
          }}
        </Await>
      </Suspense>
    </Aside>
  );
}

function CartEmpty() {
  const {close} = useAside();
  return (
    <div className="p-4 flex flex-col items-center gap-4">
      <p>Your cart is empty.</p>
      <Button to="/collections" onClick={close}>
        Continue shopping
      </Button>
    </div>
  );
}

export {CartAside};
