import {<PERSON><PERSON>t, <PERSON>} from 'react-router-dom';
import {Suspense, useId} from 'react';
import type {
  CartApiQueryFragment,
  FooterQuery,
  HeaderQuery,
} from 'storefrontapi.generated';
import {Aside} from '~/components/Aside';
import {Footer} from '~/components/Footer';
import {Header, HeaderMenu} from '~/components/Header';
import {
  SEARCH_ENDPOINT,
  SearchFormPredictive,
} from '~/components/SearchFormPredictive';
import {SearchResultsPredictive} from '~/components/SearchResultsPredictive';
import {CartAside} from './CartAside';

interface PageLayoutProps {
  cart: Promise<CartApiQueryFragment | null>;
  footer: Promise<FooterQuery | null>;
  header: HeaderQuery;
  isLoggedIn: Promise<boolean>;
  publicStoreDomain: string;
  children?: React.ReactNode;
}

export function PageLayout({
  cart,
  children = null,
  footer,
  header,
  isLoggedIn,
  publicStoreDomain,
}: PageLayoutProps) {
  return (
    <Aside.Provider>
      <CartAside cart={cart} />
      <SearchAside />
      <MobileMenuAside header={header} publicStoreDomain={publicStoreDomain} />
      {header && (
        <Header
          header={header}
          cart={cart}
          isLoggedIn={isLoggedIn}
          publicStoreDomain={publicStoreDomain}
        />
      )}
      <main>{children}</main>
      <Footer
        footer={footer}
        header={header}
        publicStoreDomain={publicStoreDomain}
      />
    </Aside.Provider>
  );
}

function SearchAside() {
  const queriesDatalistId = useId();
  return (
    <Aside type="search" heading="SEARCH">
      <div className="predictive-search">
        <br />
        <SearchFormPredictive>
          {({fetchResults, goToSearch, inputRef}) => (
            <>
              <input
                name="q"
                onChange={fetchResults}
                onFocus={fetchResults}
                placeholder="Search"
                ref={inputRef}
                type="search"
                list={queriesDatalistId}
              />
              &nbsp;
              <button onClick={goToSearch}>Search</button>
            </>
          )}
        </SearchFormPredictive>

        <SearchResultsPredictive>
          {({items, total, term, state, closeSearch}) => {
            const {articles, collections, pages, products, queries} = items;

            if (state === 'loading' && term.current) {
              return <div>Loading...</div>;
            }

            if (total === 0 && term.current) {
              return (
                <div>
                  <p>
                    No results for &quot;{term.current}&quot; found.
                    <br />
                    <Link to="/search" onClick={closeSearch}>
                      Go to search page
                    </Link>
                  </p>
                </div>
              );
            }

            return (
              <>
                <datalist id={queriesDatalistId}>
                  {queries.map(({text}) => (
                    <option key={text} value={text} />
                  ))}
                </datalist>
                {products.length > 0 && (
                  <div>
                    <h2>Products</h2>
                    <ul>
                      {products.map((product) => (
                        <li key={product.id}>
                          <Link to={`/products/${product.handle}`}>
                            {product.title}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                {articles.length > 0 && (
                  <div>
                    <h2>Articles</h2>
                    <ul>
                      {articles.map((article) => (
                        <li key={article.id}>
                          <Link to={`/blogs/${article.handle}`}>
                            {article.title}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                {pages.length > 0 && (
                  <div>
                    <h2>Pages</h2>
                    <ul>
                      {pages.map((page) => (
                        <li key={page.id}>
                          <Link to={`/pages/${page.handle}`}>{page.title}</Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                {collections.length > 0 && (
                  <div>
                    <h2>Collections</h2>
                    <ul>
                      {collections.map((collection) => (
                        <li key={collection.id}>
                          <Link to={`/collections/${collection.handle}`}>
                            {collection.title}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </>
            );
          }}
        </SearchResultsPredictive>
      </div>
    </Aside>
  );
}

function MobileMenuAside({
  header,
  publicStoreDomain,
}: {
  header: HeaderQuery;
  publicStoreDomain: string;
}) {
  return (
    <Aside type="mobile" heading="MENU">
      <HeaderMenu
        menu={header.menu}
        viewport="mobile"
        primaryDomainUrl={header.shop.primaryDomain.url}
        publicStoreDomain={publicStoreDomain}
      />
    </Aside>
  );
}
